<script lang="ts">
	import { page } from '$app/state';
	import type { PageProps } from './$types';
	import { createBudgetDataForDepth } from '$lib/budget_utils';
	import { Chart, Tooltip, Axis, Bars, Layer, Highlight } from 'layerchart';
	import { scaleBand } from 'd3-scale';
	import { sum } from 'd3-array';

	let { data }: PageProps = $props();

	const depth = $state(3);

	const stackedData = $derived(
		createBudgetDataForDepth(data.allWbsItems, data.rawCurrentItems, depth, 'current'),
	);
</script>

<div class="container">
	<h1 class="sr-only text-3xl font-semibold">Overview</h1>

	<div class="h-[300px] rounded-sm border p-4">
		<Chart
			data={stackedData}
			x="wbsCode"
			xScale={scaleBand().paddingInner(0.4).paddingOuter(0.2)}
			y="totalValue"
			yNice
			padding={{ left: 16, bottom: 24 }}
			tooltip={{ mode: 'band' }}
		>
			{#snippet children({ context })}
				<Layer type="svg">
					<Axis placement="left" grid rule />
					<Axis placement="bottom" rule />
					<Bars strokeWidth={1} />
					<Highlight area />
				</Layer>

				<Tooltip.Root>
					{#snippet children({ data })}
						<Tooltip.Header>{data.wbsCode}</Tooltip.Header>
						<Tooltip.List>
							{#each data as d (d.wbsCode)}
								<Tooltip.Item
									label={d.wbsCode}
									value={d.value}
									color={context.cScale?.(d.wbsCode)}
									format="integer"
									valueAlign="right"
								/>
							{/each}

							<Tooltip.Separator />

							<Tooltip.Item
								label="total"
								value={sum([...data], (d) => d.value)}
								format="integer"
								valueAlign="right"
							/>
						</Tooltip.List>
					{/snippet}
				</Tooltip.Root>
			{/snippet}
		</Chart>
	</div>
</div>
